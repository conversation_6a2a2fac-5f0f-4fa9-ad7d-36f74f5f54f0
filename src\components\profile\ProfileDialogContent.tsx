
import React, { useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import UsernameEditor from "./UsernameEditor";
import SimpleProfileForm from "./SimpleProfileForm";
import ChatSettings from "./ChatSettings";
import ChatRequestsList, { ChatRequest } from "./ChatRequestsList";
import ReadingActivity from "./ReadingActivity";
import AvatarManagement from "./AvatarManagement";
import { useAuth } from "@/contexts/AuthContext";
import { getUserProfile } from "@/services/profileService";

interface ProfileDialogContentProps {
  username: string;
  setUsername: (username: string) => void;
  favoriteAuthor: string;
  setFavoriteAuthor: (author: string) => void;
  favoriteGenre: string;
  setFavoriteGenre: (genre: string) => void;
  bio: string;
  setBio: (bio: string) => void;
  allowChats: boolean;
  setAllowChats: (allow: boolean) => void;
  chatRequests: ChatRequest[];
  activeChatsCount: number;
  onChatAction: (chatId: string, action: 'accept' | 'reject') => void;
}

const ProfileDialogContent: React.FC<ProfileDialogContentProps> = ({
  username,
  setUsername,
  favoriteAuthor,
  setFavoriteAuthor,
  favoriteGenre,
  setFavoriteGenre,
  bio,
  setBio,
  allowChats,
  setAllowChats,
  chatRequests,
  activeChatsCount,
  onChatAction
}) => {
  const { user } = useAuth();
  const [currentAvatarUrl, setCurrentAvatarUrl] = useState<string | null>(null);
  const [loadingAvatar, setLoadingAvatar] = useState(true);

  // Load current avatar URL
  useEffect(() => {
    const loadAvatar = async () => {
      if (!user?.id) return;

      try {
        const profile = await getUserProfile(user.id);
        setCurrentAvatarUrl(profile?.avatar_url || null);
      } catch (error) {
        console.error('Error loading avatar:', error);
      } finally {
        setLoadingAvatar(false);
      }
    };

    loadAvatar();
  }, [user?.id]);

  // Handle avatar updates
  const handleAvatarUpdate = (newAvatarUrl: string | null) => {
    setCurrentAvatarUrl(newAvatarUrl);
  };
  return (
    <div className="flex-1 overflow-hidden py-2">
      <ScrollArea className="h-full pr-4">
        <div className="space-y-6 font-serif px-2">
          {/* Avatar Management Section */}
          {!loadingAvatar && (
            <AvatarManagement
              currentAvatarUrl={currentAvatarUrl}
              onAvatarUpdate={handleAvatarUpdate}
              showRemoveOption={true}
            />
          )}

          <UsernameEditor username={username} setUsername={setUsername} />

          <SimpleProfileForm
            favoriteAuthor={favoriteAuthor}
            setFavoriteAuthor={setFavoriteAuthor}
            favoriteGenre={favoriteGenre}
            setFavoriteGenre={setFavoriteGenre}
            bio={bio}
            setBio={setBio}
          />

          <ChatSettings
            allowChats={allowChats}
            setAllowChats={setAllowChats}
            activeChatsCount={activeChatsCount}
          />

          {chatRequests.length > 0 && (
            <ChatRequestsList
              chatRequests={chatRequests}
              onChatAction={onChatAction}
            />
          )}

          <ReadingActivity />
        </div>
      </ScrollArea>
    </div>
  );
};

export default ProfileDialogContent;
