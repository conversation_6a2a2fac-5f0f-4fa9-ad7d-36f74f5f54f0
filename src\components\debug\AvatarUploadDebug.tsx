import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { updateUserAvatar } from '@/services/profileService';
import { toast } from 'sonner';

const AvatarUploadDebug: React.FC = () => {
  const { user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [testing, setTesting] = useState(false);

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => prev + '\n' + new Date().toISOString() + ': ' + info);
  };

  const testStorageBucket = async () => {
    setTesting(true);
    setDebugInfo('Starting storage bucket test...');
    
    try {
      // Test 1: Check if profiles bucket exists
      addDebugInfo('Testing bucket existence...');
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
      
      if (bucketsError) {
        addDebugInfo(`❌ Error listing buckets: ${bucketsError.message}`);
        return;
      }
      
      const profilesBucket = buckets?.find(bucket => bucket.name === 'profiles');
      if (!profilesBucket) {
        addDebugInfo('❌ Profiles bucket not found!');
        addDebugInfo(`Available buckets: ${buckets?.map(b => b.name).join(', ')}`);
        return;
      }
      
      addDebugInfo('✅ Profiles bucket exists');
      addDebugInfo(`Bucket details: ${JSON.stringify(profilesBucket)}`);
      
      // Test 2: Check bucket policies
      addDebugInfo('Testing bucket policies...');
      const { data: policies, error: policiesError } = await supabase.storage
        .from('profiles')
        .list('', { limit: 1 });
        
      if (policiesError) {
        addDebugInfo(`❌ Error accessing bucket: ${policiesError.message}`);
        return;
      }
      
      addDebugInfo('✅ Can access profiles bucket');
      
      // Test 3: Test file upload
      if (user) {
        addDebugInfo('Testing file upload...');
        const testFile = new Blob(['test'], { type: 'text/plain' });
        const testFileName = `test-${user.id}-${Date.now()}.txt`;
        const testFilePath = `avatars/${testFileName}`;
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('profiles')
          .upload(testFilePath, testFile);
          
        if (uploadError) {
          addDebugInfo(`❌ Upload failed: ${uploadError.message}`);
          return;
        }
        
        addDebugInfo('✅ Test file uploaded successfully');
        addDebugInfo(`Upload path: ${uploadData.path}`);
        
        // Test 4: Get public URL
        const { data: urlData } = supabase.storage
          .from('profiles')
          .getPublicUrl(testFilePath);
          
        addDebugInfo(`✅ Public URL: ${urlData.publicUrl}`);
        
        // Test 5: Test profile service
        addDebugInfo('Testing profile service...');
        const updatedProfile = await updateUserAvatar(user.id, urlData.publicUrl);
        
        if (!updatedProfile) {
          addDebugInfo('❌ Profile service failed to update avatar');
        } else {
          addDebugInfo('✅ Profile service updated successfully');
          addDebugInfo(`Updated avatar URL: ${updatedProfile.avatar_url}`);
        }
        
        // Cleanup: Delete test file
        await supabase.storage
          .from('profiles')
          .remove([testFilePath]);
        addDebugInfo('✅ Test file cleaned up');
      }
      
    } catch (error) {
      addDebugInfo(`❌ Unexpected error: ${error}`);
      console.error('Debug test error:', error);
    } finally {
      setTesting(false);
    }
  };

  const testImageUpload = async (file: File) => {
    if (!user) return;
    
    setTesting(true);
    setDebugInfo('Starting image upload test...');
    
    try {
      addDebugInfo(`File details: ${file.name}, ${file.type}, ${file.size} bytes`);
      
      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
      if (!validTypes.includes(file.type)) {
        addDebugInfo(`❌ Invalid file type: ${file.type}`);
        return;
      }
      
      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        addDebugInfo(`❌ File too large: ${file.size} bytes`);
        return;
      }
      
      addDebugInfo('✅ File validation passed');
      
      // Create filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;
      
      addDebugInfo(`Upload path: ${filePath}`);
      
      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profiles')
        .upload(filePath, file);
        
      if (uploadError) {
        addDebugInfo(`❌ Upload error: ${uploadError.message}`);
        addDebugInfo(`Error details: ${JSON.stringify(uploadError)}`);
        return;
      }
      
      addDebugInfo('✅ File uploaded successfully');
      addDebugInfo(`Upload data: ${JSON.stringify(uploadData)}`);
      
      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profiles')
        .getPublicUrl(filePath);
        
      addDebugInfo(`✅ Public URL: ${urlData.publicUrl}`);
      
      // Update profile
      const updatedProfile = await updateUserAvatar(user.id, urlData.publicUrl);
      
      if (!updatedProfile) {
        addDebugInfo('❌ Profile update failed');
      } else {
        addDebugInfo('✅ Profile updated successfully');
        toast.success('Test upload completed successfully!');
      }
      
    } catch (error) {
      addDebugInfo(`❌ Unexpected error: ${error}`);
      console.error('Image upload test error:', error);
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Avatar Upload Debug Tool</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={testStorageBucket} 
            disabled={testing}
            variant="outline"
          >
            Test Storage Bucket
          </Button>
          
          <label className="cursor-pointer">
            <Button 
              disabled={testing}
              variant="outline"
              asChild
            >
              <span>Test Image Upload</span>
            </Button>
            <input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) testImageUpload(file);
              }}
              disabled={testing}
            />
          </label>
          
          <Button 
            onClick={() => setDebugInfo('')}
            variant="outline"
          >
            Clear Log
          </Button>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg">
          <h4 className="font-medium mb-2">Debug Log:</h4>
          <pre className="text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
            {debugInfo || 'No debug information yet. Click a test button to start.'}
          </pre>
        </div>
      </CardContent>
    </Card>
  );
};

export default AvatarUploadDebug;
