
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

import AccountInfoForm from "@/components/profile/AccountInfoForm";
import { ProfileForm } from "@/components/profile";
import ReadingActivity from "@/components/profile/ReadingActivity";
import AvatarManagement from "@/components/profile/AvatarManagement";
import { getUserProfile } from "@/services/profileService";

const Profile = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [username, setUsername] = useState(user?.user_metadata?.username || "");
  const [favoriteAuthor, setFavoriteAuthor] = useState(user?.user_metadata?.favorite_author || "");
  const [favoriteGenre, setFavoriteGenre] = useState(user?.user_metadata?.favorite_genre || "Fiction");
  const [bio, setBio] = useState(user?.user_metadata?.bio || "");
  const [isUpdating, setIsUpdating] = useState(false);
  const [currentAvatarUrl, setCurrentAvatarUrl] = useState<string | null>(null);
  const [loadingAvatar, setLoadingAvatar] = useState(true);
  
  // Redirect if not logged in
  useEffect(() => {
    if (!user) {
      navigate("/login");
    }
  }, [user, navigate]);
  
  // Load user data when component mounts
  useEffect(() => {
    if (user) {
      setUsername(user.user_metadata?.username || "");
      setFavoriteAuthor(user.user_metadata?.favorite_author || "");
      setFavoriteGenre(user.user_metadata?.favorite_genre || "Fiction");
      setBio(user.user_metadata?.bio || "");
    }
  }, [user]);

  // Load avatar data
  useEffect(() => {
    const loadAvatar = async () => {
      if (!user?.id) return;

      try {
        const profile = await getUserProfile(user.id);
        setCurrentAvatarUrl(profile?.avatar_url || null);
      } catch (error) {
        console.error('Error loading avatar:', error);
      } finally {
        setLoadingAvatar(false);
      }
    };

    loadAvatar();
  }, [user?.id]);

  // Handle avatar updates
  const handleAvatarUpdate = (newAvatarUrl: string | null) => {
    setCurrentAvatarUrl(newAvatarUrl);
  };
  
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdating(true);
    
    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          username, 
          favorite_author: favoriteAuthor,
          favorite_genre: favoriteGenre,
          bio
        })
        .eq('id', user?.id);

      if (error) throw error;
      
      // Update local storage for consistency
      localStorage.setItem("username", username);
      localStorage.setItem("favorite_genre", favoriteGenre);
      
      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile. Please try again.");
    } finally {
      setIsUpdating(false);
    }
  };
  
  if (!user) {
    return null;
  }
  
  return (
    <div className="max-w-2xl mx-auto">
      <h1 className="text-4xl font-serif font-bold mb-8 text-bookconnect-brown">Your Profile</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left column: Avatar and basic info */}
        <div className="flex flex-col space-y-4 md:col-span-1">
          {/* Avatar Management */}
          {!loadingAvatar && (
            <AvatarManagement
              currentAvatarUrl={currentAvatarUrl}
              onAvatarUpdate={handleAvatarUpdate}
              showRemoveOption={true}
            />
          )}

          {/* User Info Card */}
          <Card className="shadow rounded-lg">
            <CardContent className="p-4">
              <div className="text-center space-y-2">
                <h2 className="text-xl font-semibold">{username}</h2>
                <p className="text-gray-600">{user.email}</p>
                <p className="text-sm text-gray-500">
                  Member since {new Date(user.created_at || Date.now()).toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column: Forms */}
        <div className="md:col-span-2 space-y-6">
          <Card className="shadow rounded-lg">
            <CardHeader>
              <CardTitle className="text-lg font-bold text-gray-800">Account Information</CardTitle>
              <CardDescription className="text-gray-500">
                Update your account details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AccountInfoForm
                email={user.email}
                username={username}
                setUsername={setUsername}
                isUpdating={isUpdating}
                onSubmit={handleUpdateProfile}
              />
            </CardContent>
          </Card>
          
          <Card className="shadow rounded-lg">
            <CardHeader>
              <CardTitle className="text-lg font-bold text-gray-800">Profile Details</CardTitle>
              <CardDescription className="text-gray-500">
                Tell the community about yourself
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProfileForm
                favoriteAuthor={favoriteAuthor}
                setFavoriteAuthor={setFavoriteAuthor}
                favoriteGenre={favoriteGenre}
                setFavoriteGenre={setFavoriteGenre}
                bio={bio}
                setBio={setBio}
              />
            </CardContent>
          </Card>

          <ReadingActivity />
        </div>
      </div>
    </div>
  );
};

export default Profile;
